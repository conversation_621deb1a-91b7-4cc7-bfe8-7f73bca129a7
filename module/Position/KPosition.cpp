#include "KPosition.h"
#include "aiEngine/KBitlandAIEnity.h"
#include "algorithm/KImageMarker.h"
#include "KFactory.h"

#pragma region 模板匹配模块
KMatchModule::KMatchModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("downSampleNum", &_downSampleNum);
    params().add("rotateEnable", &_rotateEnable);
    params().add("matchMethod", &_matchMethod);
    params().add("angleStep", &_angleStep);
    params().add("minAngle", &_minAngle);
    params().add("maxAngle", &_maxAngle);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("maxMatchscore", &_maxMatchscore);
    result().add("bestAngle", &_bestAngle);

    _tempimg = cv::imread("template.png");
    cv::cvtColor(_tempimg, _tempimg, cv::COLOR_BGR2GRAY);
    for (int i = 0; i < _downSampleNum.iValue(); i++)
    {
        cv::pyrDown(_tempimg, _tempimg, cv::Size(_tempimg.cols / 2, _tempimg.rows / 2));
    }
}

REGISTER_MODULE(KMatchModule);

int KMatchModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();

    cv::cvtColor(input, input, cv::COLOR_BGR2GRAY);
    for (int i = 0; i < _downSampleNum.iValue(); i++)
        cv::pyrDown(input, input, cv::Size(input.cols / 2, input.rows / 2));

    if (_rotateEnable.bValue())
    {
        double maxMatchscore, bestAngle;
        cv::Point location;
        RotateMatch(input, _tempimg, _matchMethod.iValue(), maxMatchscore, _location, bestAngle);
        _maxMatchscore.setValue(maxMatchscore);
        _bestAngle.setValue(bestAngle);

        cv::Rect rc = cv::Rect(_location.x, _location.y, _tempimg.cols * pow(2, _downSampleNum.iValue()), _tempimg.rows * pow(2, _downSampleNum.iValue()));
        drawTransformedRect(output, rc, bestAngle);
    }
    else
    {
        double maxMatchscore;
        MatchTemplate(input, _tempimg, _matchMethod.iValue(), maxMatchscore, _location);
        _maxMatchscore.setValue(maxMatchscore);

        cv::rectangle(output, _location, cv::Point(_location.x + _tempimg.cols * pow(2, _downSampleNum.iValue()), _location.y + _tempimg.rows * pow(2, _downSampleNum.iValue())), cv::Scalar(0, 255, 0));
    }
    _image2Out.Image2().src() = output;
    return KCV_OK;
}

cv::Mat KMatchModule::ImageRotate(cv::Mat input, double angle)
{
    cv::Mat newImg;
    cv::Point2f pt = cv::Point2f(input.cols / 2, input.rows / 2);
    cv::Mat M = cv::getRotationMatrix2D(pt, angle, 1.0);
    cv::warpAffine(input, newImg, M, input.size());
    return newImg;
}

void KMatchModule::drawTransformedRect(cv::Mat &image, const cv::Rect &rect, double angle)
{
    // 获取原始矩形的四个顶点
    std::vector<cv::Point2f> original_corners(4);
    original_corners[0] = cv::Point2f(rect.x, rect.y);                            // 左上角
    original_corners[1] = cv::Point2f(rect.x + rect.width, rect.y);               // 右上角
    original_corners[2] = cv::Point2f(rect.x + rect.width, rect.y + rect.height); // 右下角
    original_corners[3] = cv::Point2f(rect.x, rect.y + rect.height);              // 左下角

    // 变换所有顶点
    std::vector<cv::Point2f> transformed_corners(4);
    cv::Point2f center(image.cols / 2, image.rows / 2);
    cv::Mat affine_matrix = cv::getRotationMatrix2D(center, angle, 1.0);
    cv::transform(original_corners, transformed_corners, affine_matrix);

    // 转换为整数坐标用于绘制
    std::vector<cv::Point> drawing_points(4);
    for (int i = 0; i < 4; ++i)
    {
        drawing_points[i] = transformed_corners[i];
    }

    // 绘制闭合多边形
    cv::polylines(image, drawing_points, true, cv::Scalar(0, 255, 0), 2);
}

int KMatchModule::MatchTemplate(cv::Mat input, cv::Mat temp, int matchMode, double &maxVal, cv::Point &maxLoc)
{
    cv::Mat result;
    // cv::matchTemplate(input, temp, result, matchMode);
    cv::matchTemplate(input, temp, result, cv::TM_CCOEFF_NORMED);
    cv::minMaxLoc(result, 0, &maxVal, 0, &maxLoc, cv::noArray());
    maxLoc.x = maxLoc.x * pow(2, _downSampleNum.iValue());
    maxLoc.y = maxLoc.y * pow(2, _downSampleNum.iValue());
    return 0;
}

int KMatchModule::RotateMatch(cv::Mat input, cv::Mat temp, int matchMode, double &maxval, cv::Point &maxloc, double &bestangle)
{
    double maxvalall = -100.0;
    cv::Point maxLocall;
    double angle = 0.0;
    int range = _maxAngle.dValue() - _minAngle.dValue();
    for (int i = 0; i < range / _angleStep.dValue(); i++)
    {
        cv::Mat newImg = ImageRotate(temp, _minAngle.dValue() + _angleStep.dValue() * i);
        cv::Mat result;
        double minVal, maxVal;
        cv::Point minLoc, maxLoc;
        cv::matchTemplate(input, newImg, result, matchMode);
        cv::minMaxLoc(result, &minVal, &maxVal, &minLoc, &maxLoc, cv::noArray());
        if (maxvalall < maxVal)
        {
            maxvalall = maxVal;
            maxLocall = maxLoc;
            angle = _minAngle.dValue() + _angleStep.dValue() * i;
        }
    }
    maxval = maxvalall;
    maxloc = maxLocall;
    bestangle = angle;
    maxloc.x = maxloc.x * pow(2, _downSampleNum.iValue());
    maxloc.y = maxloc.y * pow(2, _downSampleNum.iValue());
    return 0;
}
#pragma endregion

#pragma region 位置修正模块
PositionCorrectKModule::PositionCorrectKModule()
    : KModule()
{
}

REGISTER_MODULE(PositionCorrectKModule);

int PositionCorrectKModule::run()
{
    if (!enable())
        return KCV_OK;
    if (_enableMaxRect.bValue())
    {
        _finalRectOut.rect().x = _finalRectIn.rect().x * _xScale.dValue() + _leftTopX.iValue();
        _finalRectOut.rect().y = _finalRectIn.rect().y * _yScale.dValue() + _leftTopX.iValue();
        _finalRectOut.rect().width = _finalRectIn.rect().width * _xScale.dValue();
        _finalRectOut.rect().height = _finalRectIn.rect().height * _yScale.dValue();
    }
    if (_enableAllRect.bValue())
    {
        for (int i = 0; i < _kRectsIn.rect1D().size(); i++)
        {
            KRect rc = _kRectsIn.rect1D()[i];
            rc.x = rc.x * _xScale.dValue() + _leftTopX.iValue();
            rc.y = rc.y * _yScale.dValue() + _leftTopY.iValue();
            rc.width = rc.width * _xScale.dValue();
            rc.height = rc.height * _yScale.dValue();
            _kRectsOut.push_back(rc);
        }
    }

    return 0;
}

#pragma endregion

#pragma region Blob分析模块
KBlobModule::KBlobModule()
    : KModule()
{
    // 后续完善
}

REGISTER_MODULE(KBlobModule);

bool contoursSortByArea(std::vector<cv::Point> &b1, std::vector<cv::Point> &b2)
{
    int a1 = cv::contourArea(b1);
    int a2 = cv::contourArea(b2);
    return a1 > a2;
}

int KBlobModule::run()
{
    if (!enable())
        return KCV_OK;
    // 输入为二值图，比如分割算法结果
    if (_thresholdType.iValue() == 0)
    {
        if (_segmentResultIndex.iValue() > _channelNum.iValue())
        { // 设置类别数错误，超出类别总数
            return Algo_Return_Segment;
        }
        // 获取轮廓信息，定位框信息
        KImage2 img = _binaryImages.Image2Data(_segmentResultIndex.iValue()).Image2();
        KContours kcons = img.contours();
        if (kcons.count() == 0)
        {
            return Algo_Return_Segment;
        }
        // 查找个数为1时，返回最大轮廓和最大检测框
        if (_blobNumber.iValue() == 1)
        {
            _contours = kcons;
            _kRects = _contours.rects();
            int maxidx = _contours.maxArea();
            _maxIndex.setValue(maxidx);
            _finalRectOut = _kRects.rect1D()[maxidx];
        }
        else
        { // 检测多个轮廓, 过滤排序
            for (int i = 0; i < kcons.count(); i++)
            {
                int area = cv::contourArea(kcons.coutours[i]);
                cv::Rect rc = cv::boundingRect(kcons.coutours[i]);
                if (_enableLimitWidth.bValue() && rc.width < _minWidth.iValue())
                {
                    continue;
                }
                if (_enableLimitHeight.bValue() && rc.height < _minHeight.iValue())
                {
                    continue;
                }
                if (_enableLimitArea.bValue() && area < _minArea.iValue())
                {
                    continue;
                }
                _contours.coutours.push_back(kcons.coutours[i]);
                _kRects.push_back(rc);
            }
            if (_contours.count() > 0)
            {
                int maxidx = _contours.maxArea();
                _maxIndex.setValue(maxidx);
                _finalRectOut = _kRects.rect1D()[maxidx];
                _objectNumber = _contours.count();
            }
            else
            {
                return Algo_Return_Segment;
            }
        }
    }

    return 0;
}
#pragma endregion

#pragma region 圆查找模块
KFindCircleModule::KFindCircleModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("minRadius", &_minRadius);
    params().add("maxRadius", &_maxRadius);
    params().add("threshold", &_threshold);
    params().add("minDist", &_minDist);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KFindCircleModule);

int KFindCircleModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;
    cv::Mat gray;
    if (input.channels() == 3)
        cv::cvtColor(input, gray, cv::COLOR_BGR2GRAY);
    else
        gray = input;

    cv::GaussianBlur(gray, gray, cv::Size(5, 5), 0);
    std::vector<cv::Vec3f> circles;
    cv::HoughCircles(gray, circles, cv::HOUGH_GRADIENT,
                     1,                   // dp
                     _minDist.iValue(),   // minDist (查找圆间距)
                     _threshold.iValue(), // param1 (Canny的高阈值)
                     50,                  // param2 (累加器阈值，需要调试)
                     _minRadius.iValue(), // minRadius (使用你的参数)
                     _maxRadius.iValue()  // maxRadius (使用你的参数)
    );

    if (!circles.empty())
    {
        for (int i = 0; i < circles.size(); i++)
        {
            // 通常，当minDist很大时，这里只会有一个结果
            cv::Point center(cvRound(circles[i][0]), cvRound(circles[i][1]));
            int radius = cvRound(circles[i][2]);
            cv::circle(output, center, radius, cv::Scalar(0, 255, 0), 2, cv::LINE_AA);
            cv::circle(output, center, 3, cv::Scalar(0, 0, 255), -1, cv::LINE_AA);
        }
        std::cout << "HoughCircles 找到 " << circles.size() << " 个圆！" << std::endl;
    }
    else
    {
        std::cout << "HoughCircles 未找到任何圆！" << std::endl;
    }
    return 0;
}
#pragma endregion

#pragma region 椭圆查找模块
KFindEllipseModule::KFindEllipseModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("minRadius", &_minRadius);
    params().add("maxRadius", &_maxRadius);
    params().add("threshold", &_threshold);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
}

REGISTER_MODULE(KFindEllipseModule);

int KFindEllipseModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;

    cv::Mat gray;
    if (input.channels() == 3)
        cv::cvtColor(input, gray, cv::COLOR_BGR2GRAY);
    else
        gray = input;

    cv::GaussianBlur(gray, gray, cv::Size(5, 5), 0);
    std::vector<cv::Vec3f> ellipses;
    cv::Canny(gray, gray, 50, 150);
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(gray, contours, hierarchy, cv::RETR_LIST, cv::CHAIN_APPROX_SIMPLE);
    for (const auto &contour : contours)
    {
        if (contour.size() < 20)
        { // 稍微提高点数下限
            continue;
        }

        // --- 核心优化步骤：简化轮廓 ---
        std::vector<cv::Point> approx_contour;
        // 计算 epsilon 值，通常是轮廓周长的某个百分比
        // 1-5% 是一个很好的起始范围
        double epsilon = 0.01 * cv::arcLength(contour, true);
        cv::approxPolyDP(contour, approx_contour, epsilon, true);

        // 检查简化后的轮廓点数是否仍然足够拟合椭圆
        if (approx_contour.size() < 5)
        {
            continue;
        }

        std::cout << "Original points: " << contour.size()
                  << ", Approximated points: " << approx_contour.size() << std::endl;

        // --- 使用简化后的轮廓进行拟合 ---
        cv::RotatedRect fittedEllipse = cv::fitEllipse(approx_contour);

        // 绘制结果以供对比
        // 用红色绘制原始轮廓
        // cv::polylines(image, contour, true, cv::Scalar(0, 0, 255), 1);
        // 用绿色绘制拟合的椭圆
        cv::ellipse(output, fittedEllipse, cv::Scalar(0, 255, 0), 2);
    }
    return 0;
}
#pragma endregion

#pragma region 直线查找模块
#include <random>
KFindLineModule::KFindLineModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("roi", &_roi);
    params().add("edgePolarType", &_edgePolarType);
    params().add("binaryThreshold", &_binaryThreshold);
    params().add("minPoints", &_minPoints);
    params().add("fitMethod", &_fitMethod);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("ret", &_ret);
    result().add("angle", &_angle);
}

REGISTER_MODULE(KFindLineModule);

int KFindLineModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    cv::Mat gray = input(_roi.rect()).clone();
    cv::Mat edge;
    cv::cvtColor(gray, gray, cv::COLOR_RGB2GRAY);
    cv::Canny(gray, edge, 50, 150);
    // 获取边缘点 (转换为2D点集)
    vector<cv::Point> points;
    for (int y = 0; y < edge.rows; y++)
    {
        for (int x = 0; x < edge.cols; x++)
        {
            if (edge.at<uchar>(y, x) > 0)
            {
                points.push_back(cv::Point2f(x, y));
                cv::circle(output, Point(x + _roi.rect().x, y + _roi.rect().y), 2, cv::Scalar(255, 0, 0));
            }
        }
    }
    if (points.size() < _minPoints.iValue())
    {
        _ret.setValue(1);
        return KCV_NG;
    }
    // 不同方法拟合直线
    cv::Vec4f line_params; // 点斜式
    switch (_fitMethod.iValue())
    {
    case 0:
        cv::fitLine(points, line_params, cv::DIST_L2, 0, 0.1, 0.1);
        break;
    case 1:
        cv::fitLine(points, line_params, cv::DIST_HUBER, 0, 0.1, 0.1);
        break;
    case 2:
        // RANSAC 参数
        const int max_iterations = 100;
        const float distance_threshold = 2.0; // 内点距离阈值
        int best_inliers = 0;
        if (!points.empty())
        {
            std::random_device rd;
            std::mt19937 gen(rd());
            std::uniform_int_distribution<> dist(0, points.size() - 1);

            for (int i = 0; i < max_iterations; i++)
            {
                // 随机选择两个点
                int idx1 = dist(gen);
                int idx2 = dist(gen);
                while (idx2 == idx1)
                    idx2 = dist(gen);

                cv::Point p1 = points[idx1];
                cv::Point p2 = points[idx2];

                // 计算直线参数 (两点式)
                float vx = p2.x - p1.x;
                float vy = p2.y - p1.y;
                float norm = std::sqrt(vx * vx + vy * vy);
                if (norm < 1e-5)
                    continue; // 避免除零

                vx /= norm;
                vy /= norm;

                // 计算点到直线距离并统计内点
                int inliers = 0;
                for (const auto &pt : points)
                {
                    float distance = std::abs((pt.x - p1.x) * vy - (pt.y - p1.y) * vx);
                    if (distance < distance_threshold)
                        inliers++;
                }

                // 更新最佳模型
                if (inliers > best_inliers)
                {
                    best_inliers = inliers;
                    line_params = cv::Vec4f(vx, vy, p1.x, p1.y);
                }
            }
        } // RANSAC end
        break;
    }
    // 提取直线参数
    float vx = line_params[0];
    float vy = line_params[1];
    float x0 = line_params[2];
    float y0 = line_params[3];
    double k = vy / vx;
    double angle_rad = atan2(vy, vx);
    double angle_deg = angle_rad * 180 / CV_PI;
    float angle = 0.0;
    if (angle_deg < 0)
        angle = angle_deg + 180;
    else if (angle_deg >= 180)
        angle = angle_deg - 180;
    else
        angle = angle_deg;
    _angle.setValue(angle);
    cv::Rect roi = _roi.rect();
    cv::Point startPoint;          // 直线起点
    cv::Point endPoints;           // 直线终点
    cv::Point midPoints;           // 直线中点
    if (angle > 45 && angle < 135) // 竖向
    {
        startPoint.y = roi.y + 0;
        startPoint.x = (startPoint.y - y0) / k + x0 + roi.x;
        endPoints.y = roi.y + roi.height;
        endPoints.x = (endPoints.y - y0) / k + x0 + roi.x;
        midPoints.y = roi.y + roi.height / 2;
        midPoints.x = (midPoints.y - y0) / k + x0 + roi.x;
    }
    else
    {
        startPoint.x = roi.x + 0;
        startPoint.y = k * (startPoint.x - x0) + y0 + roi.y;
        endPoints.x = roi.x + roi.width;
        endPoints.y = k * (endPoints.x - x0) + y0 + roi.y;
        midPoints.x = roi.x + roi.width / 2;
        midPoints.y = k * (midPoints.x - x0) + y0 + roi.y;
    }

    // 绘制结果
    cv::line(output, startPoint, endPoints, cv::Scalar(255, 255, 0), 2);
    _ret.setValue(1);
    return KCV_OK;
}

#pragma endregion

#pragma region 字符定位模块（深度学习）
struct BOX
{
    cv::Rect rc;
    double prob;
};

bool bbSortByProb(BOX &b1, BOX &b2)
{
    return b1.prob > b2.prob;
}

int bInBox(std::vector<cv::Rect> &rcs, cv::Rect &rc)
{
    int bIn = 0, num = rcs.size();
    for (int i = 0; i < num; i++)
    {
        if ((rcs[i] & rc).area() * 1.0 / (rcs[i] | rc).area() > 0.5)
        {
            bIn = 1;
            break;
        }
    }

    return bIn;
}

int PostProcess_det_x(float *out, float sensor, int w, int h, float rw, float rh, std::vector<cv::Rect> &rects)
{
    float *p = out;
    BOX bb;
    std::vector<BOX> bbs;

    for (int i = 0; i < h; i++, p += 5)
    {
        bb.prob = p[w - 1];
        if (bb.prob > sensor)
        {
            int x1 = max(0, int(p[0])) * rw, x2 = max(0, int(p[2])) * rw;
            int y1 = max(0, int(p[1])) * rh, y2 = max(0, int(p[3])) * rh;
            bb.rc = cv::Rect(cv::Point(x1, y1), cv::Point(x2, y2));

            bbs.emplace_back(bb);
        }
    }

    sort(bbs.begin(), bbs.end(), bbSortByProb);
    for (int i = 0; i < bbs.size(); i++)
    {
        if (!bInBox(rects, bbs[i].rc))
        {
            rects.emplace_back(bbs[i].rc);
        }
    }

    return rects.size();
}

KCharPositionDLModule::KCharPositionDLModule()
{
    /*输入参数*/
    params().add("image2In", &_image2In);
    params().add("smartAi", &_smartAi);
    KSmartParamAI *ai = new KSmartParamAI;
    _smartAi.setAIParam(ai);

    /*输出参数*/
    result().add("image2Out", &_image2Out);
    result().add("rects", &_rects);
}

REGISTER_MODULE(KCharPositionDLModule);

int KCharPositionDLModule::run()
{
    cv::Mat input = _image2In.Image2().src();
    cv::Mat output = input.clone();
    _image2Out.Image2().src() = output;

    KAIEngineInferEnity *pRegEnity = _smartAi.smartAiData()->enity();
    KBitlandAIEngineInferEnity *bRegEnity = dynamic_cast<KBitlandAIEngineInferEnity *>(pRegEnity);
    if (!bRegEnity)
    {
        return KCV_NG;
    }
    bRegEnity->infer(_image2In.KImage2Ptr());
    KBitlandAIEngineInferData *data = (KBitlandAIEngineInferData *)(bRegEnity->data());
    std::vector<cv::Rect> rects;

    float iw = input.cols * 1.0 / data->InputDim[0].Width;
    float ih = input.rows * 1.0 / data->InputDim[0].Height;
    if (!PostProcess_det_x((float *)data->rltdata, 0.5f, data->OutputDim[0].Height, data->OutputDim[0].Channel, iw, ih, rects))
    {
        return KCV_NG;
    }
    for (int i = 0; i < rects.size(); i++)
    {
        cv::rectangle(output, rects[i], cv::Scalar(0, 255, 0), 2);
    }
    _rects.setRect(rects);
    return KCV_OK;
}
#pragma endregion
